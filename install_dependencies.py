import sys
import os
import re
import ast
import json
import shutil
import platform
import subprocess
from dataclasses import dataclass
from typing import Dict, List, Set, Tuple, Optional

# install_dependencies.py
# Comprehensive installer for SPY options trading project
# - Scans codebase for imports
# - Detects package manager (pip/conda)
# - Installs prod/dev dependencies with progress and error handling
# - Verifies installation with import checks and basic version compatibility

PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
PY_MIN = (3, 10)

MODULE_TO_PYPI: Dict[str, str] = {
    # Core
    "numpy": "numpy",
    "pandas": "pandas",
    "yfinance": "yfinance",
    "gymnasium": "gymnasium",
    "joblib": "joblib",
    "ta": "ta",
    "quantstats": "quantstats",
    "torch": "torch",
    "optuna": "optuna",
    "stable_baselines3": "stable-baselines3",
    # Requests stack
    "requests": "requests",
    "requests_cache": "requests-cache",
    "requests_ratelimiter": "requests-ratelimiter",
    "pyrate_limiter": "pyrate-limiter",
    # Options
    "py_vollib": "py_vollib",
    # Optional frameworks
    "tensortrade": "tensortrade",
}

# Prefer installing these via conda when selected
CONDA_PREFERRED: Set[str] = {
    "numpy", "pandas", "optuna", "gymnasium", "stable-baselines3", "pyrate-limiter",
    # pytorch is special-cased
}

CRITICAL_IMPORTS: List[str] = [
    "numpy", "pandas", "yfinance", "gymnasium", "joblib", "ta", "quantstats",
    "torch", "optuna", "stable_baselines3", "requests", "requests_cache",
    "requests_ratelimiter", "pyrate_limiter", "py_vollib", "matplotlib", "seaborn",
]

DEV_EXTRAS: List[str] = [
    "black", "ruff", "flake8", "pytest", "pytest-cov", "mypy", "ipykernel", "jupyter",
]


# --- Colab and environment helpers ---
# Build reverse map: PyPI package -> set of importable module names
PYPI_TO_MODULES: Dict[str, Set[str]] = {}
for _mod, _pkg in MODULE_TO_PYPI.items():
    PYPI_TO_MODULES.setdefault(_pkg, set()).add(_mod)

def _try_import(name: str) -> bool:
    try:
        __import__(name)
        return True
    except Exception:
        return False

def is_colab() -> bool:
    return (
        "COLAB_RELEASE_TAG" in os.environ
        or "COLAB_GPU" in os.environ
        or _try_import("google.colab")
    )

def has_nvidia_gpu() -> bool:
    try:
        proc = subprocess.run(["nvidia-smi", "-L"], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
        return proc.returncode == 0 and "GPU" in (proc.stdout or "")
    except Exception:
        return False

def is_importable(module_name: str) -> bool:
    return _try_import(module_name)

def is_pip_pkg_installed(pypi_name: str) -> bool:
    # Uses pip show to check if installed in current interpreter
    try:
        code, _ = run([sys.executable, "-m", "pip", "show", pypi_name])
        return code == 0
    except Exception:
        return False

@dataclass
class Args:
    mode: str = "prod"  # prod|dev
    package_manager: str = "auto"  # auto|pip|conda
    dry_run: bool = False
    cuda: str = "cpu"  # cpu|cu121|cu122|cu118 etc. for pip torch index selection
    extras: List[str] = None
    skip: List[str] = None
    save_config: bool = False  # Save preferences to config file


def load_config() -> Dict[str, str]:
    """Load saved configuration from .install_config.json"""
    config_path = os.path.join(PROJECT_ROOT, ".install_config.json")
    if os.path.exists(config_path):
        try:
            with open(config_path, "r") as f:
                return json.load(f)
        except Exception:
            pass
    return {}

def save_config(args: Args) -> None:
    """Save configuration to .install_config.json"""
    config_path = os.path.join(PROJECT_ROOT, ".install_config.json")
    config = {
        "mode": args.mode,
        "package_manager": args.package_manager,
        "cuda": args.cuda,
    }
    try:
        with open(config_path, "w") as f:
            json.dump(config, f, indent=2)
        print(f"[CONFIG] Preferences saved to {config_path}")
    except Exception as e:
        print(f"[WARN] Could not save config: {e}")

def parse_args(argv: List[str]) -> Args:
    import argparse
    
    # Load saved config as defaults
    config = load_config()
    
    p = argparse.ArgumentParser(description="Install project dependencies")
    p.add_argument("--mode", choices=["prod", "dev"], default=config.get("mode", "prod"))
    p.add_argument("--package-manager", choices=["auto", "pip", "conda"], default=config.get("package_manager", "auto"))
    p.add_argument("--dry-run", action="store_true")
    p.add_argument("--cuda", default=config.get("cuda", "cpu"), help="Torch build: cpu or cu121/cu122/etc for pip; conda uses defaults")
    p.add_argument("--extras", default="", help="Comma-separated extra pip packages to include")
    p.add_argument("--skip", default="", help="Comma-separated package names to skip (PyPI names)")
    p.add_argument("--save-config", action="store_true", help="Save current settings as defaults")
    args = p.parse_args(argv)
    return Args(
        mode=args.mode,
        package_manager=args.package_manager,
        dry_run=args.dry_run,
        cuda=args.cuda,
        extras=[x.strip() for x in args.extras.split(",") if x.strip()],
        skip=[x.strip() for x in args.skip.split(",") if x.strip()],
        save_config=args.save_config,
    )


def check_python_version() -> None:
    if sys.version_info < PY_MIN:
        print(f"[WARN] Python {PY_MIN[0]}.{PY_MIN[1]}+ is recommended. Current: {platform.python_version()}")


def detect_package_manager(preferred: str) -> str:
    # Colab optimization: prefer pip, conda is not available by default
    if is_colab():
        return "pip" if preferred in ("auto", "pip") else preferred
    if preferred in ("pip", "conda"):
        return preferred
    conda_exe = shutil.which("conda")
    if conda_exe:
        return "conda"
    return "pip"


def list_python_files(root: str) -> List[str]:
    py_files = []
    for dirpath, dirnames, filenames in os.walk(root):
        # Skip caches and model/log dirs
        skip_dirs = {"__pycache__", ".git", ".venv", "venv", "sb3_logs", "rl_logs_yfinance_options_v3.1", "models", "models_options_v3.1", "yfinance.cache"}
        if any(part in skip_dirs for part in dirpath.replace("\\", "/").split("/")):
            continue
        for fn in filenames:
            if fn.endswith(".py"):
                py_files.append(os.path.join(dirpath, fn))
    return py_files


def extract_imports(py_path: str) -> Set[str]:
    modules: Set[str] = set()
    try:
        with open(py_path, "r", encoding="utf-8") as f:
            node = ast.parse(f.read(), filename=py_path)
        for n in ast.walk(node):
            if isinstance(n, ast.Import):
                for alias in n.names:
                    root = alias.name.split(".")[0]
                    modules.add(root)
            elif isinstance(n, ast.ImportFrom):
                if n.module:
                    root = n.module.split(".")[0]
                    modules.add(root)
    except Exception as e:
        print(f"[WARN] Failed to parse {py_path}: {e}")
    return modules


def map_modules_to_packages(mods: Set[str]) -> Dict[str, str]:
    pkgs: Dict[str, str] = {}
    for m in mods:
        if m in MODULE_TO_PYPI:
            pkgs[m] = MODULE_TO_PYPI[m]
    return pkgs


def build_dependency_sets(mode: str, extras: Optional[List[str]]) -> Tuple[Set[str], Set[str]]:
    # scan codebase
    py_files = list_python_files(PROJECT_ROOT)
    mods: Set[str] = set()
    for f in py_files:
        mods |= extract_imports(f)
    mapped = map_modules_to_packages(mods)

    required: Set[str] = set(mapped.values())
    # Explicitly ensure criticals present even if not parsed
    required |= {MODULE_TO_PYPI[m] for m in CRITICAL_IMPORTS if m in MODULE_TO_PYPI}

    optional: Set[str] = set()
    if "tensortrade" in mods:
        optional.add("tensortrade")

    if mode == "dev":
        required |= set(DEV_EXTRAS)

    if extras:
        required |= set(extras)

    return required, optional


def run(cmd: List[str], cwd: Optional[str] = None) -> Tuple[int, str]:
    try:
        print(f"[CMD] {' '.join(cmd)}")
        proc = subprocess.run(cmd, cwd=cwd or PROJECT_ROOT, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
        return proc.returncode, proc.stdout
    except FileNotFoundError as e:
        return 127, str(e)


def pip_install(pkg: str, dry: bool) -> Tuple[bool, str]:
    if dry:
        return True, "DRY-RUN: pip install"
    code, out = run([sys.executable, "-m", "pip", "install", pkg])
    return code == 0, out


def pip_install_torch(cuda: str, dry: bool) -> Tuple[bool, str]:
    index = None
    if cuda and cuda != "cpu":
        # Map to official torch cu wheels index (example for cu121/cu122)
        # Users can adjust if mismatch
        index = f"https://download.pytorch.org/whl/{cuda}"
    args = [sys.executable, "-m", "pip", "install", "torch", "--extra-index-url", index] if index else [sys.executable, "-m", "pip", "install", "torch"]
    if dry:
        return True, "DRY-RUN: " + " ".join(args)
    code, out = run(args)
    return code == 0, out


def conda_install(pkg: str, dry: bool, channel: str = "conda-forge") -> Tuple[bool, str]:
    if dry:
        return True, "DRY-RUN: conda install"
    code, out = run(["conda", "install", "-y", "-c", channel, pkg])
    return code == 0, out


def install_plan(manager: str, pkgs: Set[str], cuda: str) -> List[Tuple[str, str]]:
    plan: List[Tuple[str, str]] = []
    # Ensure torch before sb3
    if "torch" in pkgs:
        plan.append(("torch", manager))
    for p in sorted(pkgs):
        if p == "torch":
            continue
        plan.append((p, manager))
    return plan


def execute_install(plan: List[Tuple[str, str]], cuda: str, dry: bool, skip: Set[str]) -> Dict[str, Tuple[bool, str]]:
    results: Dict[str, Tuple[bool, str]] = {}
    total = len(plan)
    for idx, (pkg, manager) in enumerate(plan, 1):
        if pkg in skip:
            print(f"[{idx}/{total}] SKIP {pkg}")
            results[pkg] = (True, "Skipped by user")
            continue
        print(f"[{idx}/{total}] Installing {pkg} via {manager}...")
        ok, out = False, ""
        try:
            if pkg == "torch":
                if manager == "conda":
                    # Prefer conda-forge pytorch cpu by default; CUDA users may customize
                    ok, out = conda_install("pytorch", dry)
                    if not ok:
                        print("[INFO] Falling back to pip for torch")
                        ok, out = pip_install_torch(cuda, dry)
                else:
                    ok, out = pip_install_torch(cuda, dry)
            else:
                if manager == "conda" and pkg in CONDA_PREFERRED:
                    ok, out = conda_install(pkg, dry)
                    if not ok:
                        print("[INFO] Falling back to pip for", pkg)
                        ok, out = pip_install(pkg, dry)
                else:
                    ok, out = pip_install(pkg, dry)
        except Exception as e:
            ok, out = False, f"Exception: {e}"
        print(out.strip().splitlines()[-1] if out else "")
        results[pkg] = (ok, out)
    return results



def verify_installation() -> Tuple[bool, List[str]]:
    failed: List[str] = []

    def try_import(name: str) -> bool:
        try:
            __import__(name)
            return True
        except Exception:
            return False

    all_ok = True
    for m in CRITICAL_IMPORTS:
        if not try_import(m):
            failed.append(m)
            all_ok = False
    # Version checks
    try:
        import stable_baselines3 as sb3  # type: ignore
        from packaging.version import Version
        sb3_ver = Version(getattr(sb3, "__version__", "0"))
        if sb3_ver < Version("2.3.0"):
            print(f"[WARN] stable-baselines3 >= 2.3.0 recommended, found {sb3_ver}")
    except Exception:
        pass
    try:
        import gymnasium as gym  # type: ignore
        gv = getattr(gym, "__version__", "")
        if gv and not gv.startswith("0.29"):
            print(f"[INFO] Gymnasium 0.29.x recommended for SB3 v2; found {gv}")
    except Exception:
        pass
    # Torch CUDA check
    try:
        import torch  # type: ignore
        if torch.cuda.is_available():
            print(f"[INFO] Torch CUDA available: {torch.version.cuda}")
        else:
            print("[INFO] Torch CUDA not available (CPU build or no GPU)")
    except Exception:
        pass
    return all_ok, failed


def main(argv: List[str]) -> int:
    args = parse_args(argv)
    check_python_version()

    # Save config if requested
    if args.save_config:
        save_config(args)

    manager = detect_package_manager(args.package_manager)
    print(f"[ENV] Package manager: {manager}")
    print(f"[ENV] Platform: {platform.platform()}")
    print(f"[ENV] CUDA mode: {args.cuda}")

    required, optional = build_dependency_sets(args.mode, args.extras)

    # Auto-detect environment and set sensible defaults
    if is_colab():
        print("[ENV] Google Colab detected")
        already, missing = [], []
        for p in sorted(required):
            mods = PYPI_TO_MODULES.get(p, {p})
            if any(is_importable(m) for m in mods) or is_pip_pkg_installed(p):
                already.append(p)
            else:
                missing.append(p)
        if already:
            print(f"[ENV] Colab preinstalled (skipping): {', '.join(already)}")
        required = set(missing)
        
        # Only suggest CUDA if user hasn't explicitly set CPU and GPU is available
        if has_nvidia_gpu() and args.cuda == "cpu" and not any("--cuda" in arg for arg in sys.argv):
            print("[INFO] Colab GPU detected. Using CPU build as requested.")
            print("[INFO] To use GPU acceleration, run with: --cuda cu121")
    else:
        print("[ENV] Local environment detected (Windows/Linux)")

    if args.skip:
        required -= set(args.skip)

    # Ensure install order and plan
    plan = install_plan(manager, required, args.cuda)

    print("\n[PLAN] Required packages (PyPI names):")
    print(", ".join(sorted(required)))
    if optional:
        print("[PLAN] Optional packages detected (not auto-installed):" )
        print(", ".join(sorted(optional)))

    if args.dry_run:
        print("\n[DRY-RUN] No changes made.")
        return 0

    print("\n[START] Installing packages...")
    results = execute_install(plan, args.cuda, args.dry_run, set(args.skip or []))

    failures = {p: out for p, (ok, out) in results.items() if not ok}
    if failures:
        print("\n[RESULT] Some installations failed:")
        for p, out in failures.items():
            print(f"  - {p}: see logs above")
    else:
        print("\n[RESULT] All requested packages installed successfully.")

    print("\n[VERIFY] Importing critical packages...")
    ok, missing = verify_installation()
    if not ok:
        print("[VERIFY] Missing or failed imports:")
        for m in missing:
            pypi = MODULE_TO_PYPI.get(m, m)
            print(f"  - {m} (pip name: {pypi})")
        return 1

    print("\n[DONE] Environment looks good.")
    print("\nUsage examples:")
    print("  # Basic installation (CPU mode, prod dependencies)")
    print("  python install_dependencies.py")
    print("  # Save current settings as defaults")
    print("  python install_dependencies.py --save-config")
    print("  # Development mode with extra packages")
    print("  python install_dependencies.py --mode dev")
    print("  # Use GPU acceleration (if available)")
    print("  python install_dependencies.py --cuda cu121")
    print("  # Dry run to see what would be installed")
    print("  python install_dependencies.py --dry-run")
    return 0


if __name__ == "__main__":
    sys.exit(main(sys.argv[1:]))

